/*
 * @作者: roam1n
 * @版本: 0.0.3
 * @最后更新: 2025-04-06
 */

const TODAY = new Date();
TODAY.setHours(0, 0, 0, 0);

// 工具函数：获取当前大周期
function getCurrentMacroCycle(today) {
  return input?.marco ? dv.page(input?.marco) : dv.pages(`"Nexus/周期"`)
      .sort(p => p.file.name)
      .find(p => {
          const start = new Date(p.开始);
          const done = new Date(p.完成);
          return p.周期数量 > 0 &&
                 today.getTime() >= start.getTime() && 
                 today.getTime() <= done.getTime();
      });
}

// input {macro: 大周期}
const MACRO = getCurrentMacroCycle(TODAY);

// 需要的数据
const [MACRO_CODE, MACRO_NAME] = MACRO.file.name.split('=');
const MICRO_LIST = dv.pages(`"Nexus/周期/${MACRO_CODE}"`).sort(p => p.file.name);

// 旅程类型和对应图标
const JOURNEY_TYPES = {
  "学习": "📖",
  "实践": "🏗️",
  "练习": "🌊",
  "拓展": "🪐",
  "娱乐": "🎨",
  "循环": "🔄"
};

// 创建容器
const container = dv.container;
container.style.width = "100%";
container.style.height = "100%";
container.style.position = "relative";
container.style.backgroundColor = "var(--background-primary)";
container.style.marginBottom = "12px";

// 小周期列表
const microList = document.createElement("div");
microList.style.position = "relative";
microList.style.backgroundColor = "var(--background-secondary)";
microList.style.borderRadius = "4px";
microList.style.margin = "0 12px";
microList.style.padding = "10px";
microList.style.boxSizing = "border-box";
container.appendChild(microList);

// 创建标题
const titleElement = document.createElement("div");
titleElement.textContent = "小周期";
titleElement.style.margin = "0 0 10px 0";
titleElement.style.color = "var(--text-normal)";
titleElement.style.fontWeight = "bold";
titleElement.style.marginBottom = "6px";
titleElement.style.color = "var(--text-normal)";
titleElement.style.opacity = "0.9";
titleElement.style.fontSize = "0.8em";
microList.appendChild(titleElement);

// 创建周期列表容器
const microListContainer = document.createElement("div");
microListContainer.style.display = "flex";
microListContainer.style.flexWrap = "wrap";
microListContainer.style.gap = "8px";
microListContainer.style.padding = "4px";
microList.appendChild(microListContainer);

// 计算当前日期
const currentDate = new Date();
const currentDateStr = currentDate.toISOString().split('T')[0];

// 分类存储周期
const completedMicroList = [];
const ongoingMicroList = [];
const plannedMicroList = [];
let unplannedCount = 0;

if (MACRO && MICRO_LIST) {
  // 计算未计划周期数量
  unplannedCount = MACRO.周期数量 ? MACRO.周期数量 - MICRO_LIST.length : 0;
  
  // 对周期进行分类
  MICRO_LIST.forEach(micro => {
    if (!micro.开始 || !micro.周期排期) return;
    
    micro.startDate = new Date(micro.开始);
    const durationDays = (micro.周期排期.length * micro.周期循环) - 1;
    micro.endDate = new Date(micro.startDate);
    micro.endDate.setDate(micro.startDate.getDate() + durationDays);
    

    // 判断周期类型
    if (micro.endDate.getTime() < currentDate.getTime()) {
      completedMicroList.push(micro);
    } else if (micro.startDate.getTime() <= currentDate.getTime() && currentDate.getTime() <= micro.endDate.getTime()) {
      ongoingMicroList.push(micro);
    } else if (micro.startDate.getTime() > currentDate.getTime()) {
      plannedMicroList.push(micro);
    }
  });
}

// 创建周期元素的函数
function createMicroElement(micro, type) {
  const microElement = document.createElement("div");
  microElement.className = "cycle-item " + type;
  microElement.style.padding = "10px";
  microElement.style.borderRadius = "6px";
  microElement.style.transition = "all 0.2s ease";
  
  // 根据类型设置不同样式
  switch(type) {
    case "ongoing":
      microElement.style.backgroundColor = "rgba(76, 175, 80, 0.15)";
      microElement.style.borderLeft = "4px solid rgba(76, 175, 80, 0.7)";
      break;
    case "completed":
      microElement.style.backgroundColor = "rgba(96, 125, 139, 0.15)";
      microElement.style.borderLeft = "4px solid rgba(96, 125, 139, 0.7)";
      break;
    case "planned":
      microElement.style.backgroundColor = "rgba(33, 150, 243, 0.15)";
      microElement.style.borderLeft = "4px solid rgba(33, 150, 243, 0.7)";
      break;
    case "unplanned":
      microElement.style.backgroundColor = "rgba(158, 158, 158, 0.15)";
      microElement.style.borderLeft = "4px solid rgba(158, 158, 158, 0.7)";
      break;
  }
  
  // 鼠标悬停效果
  microElement.addEventListener("mouseenter", () => {
    microElement.style.transform = "translateY(-2px)";
    microElement.style.boxShadow = "0 4px 8px rgba(0, 0, 0, 0.1)";
  });
  
  microElement.addEventListener("mouseleave", () => {
    microElement.style.transform = "translateY(0)";
    microElement.style.boxShadow = "none";
  });
  
  // 如果是未计划周期，只显示数量
  if (type === "unplanned") {
    microElement.style.display = "flex";
    microElement.style.justifyContent = "center";
    microElement.style.alignItems = "center";
    const cycleTip = document.createElement("div");
    cycleTip.style.fontSize = "0.8em";
    cycleTip.style.color = "var(--text-muted)";
    cycleTip.style.fontWeight = "200";
    cycleTip.style.writingMode = "vertical-rl";
    cycleTip.textContent = `未计划周期 (${unplannedCount})`;
    microElement.appendChild(cycleTip);
    return microElement;
  }
  
  // 提取周期编号
  const microNumber = micro.file.name.split("=")[1] || "未知";
  
  const startDateStr = (micro.startDate.getMonth() < 8 ? "0" : "") + (micro.startDate.getMonth() + 1) + "-" + (micro.startDate.getDate() < 10 ? "0" : "") + micro.startDate.getDate();
  const endDateStr = (micro.endDate.getMonth() < 8 ? "0" : "") + (micro.endDate.getMonth() + 1) + "-" + (micro.endDate.getDate() < 10 ? "0" : "") + micro.endDate.getDate();
  
  // 创建周期头部信息
  const header = document.createElement("div");
  header.style.display = "flex";
  header.style.justifyContent = "space-between";
  header.style.marginBottom = "8px";

  const dateRange = document.createElement("div");
  dateRange.style.fontSize = "0.8em";
  dateRange.style.color = "var(--text-muted)";
  dateRange.textContent = `${startDateStr}/${endDateStr}`;
  
  const microTitle = document.createElement("a");
  microTitle.style.fontWeight = "bold";
  microTitle.className = "internal-link";
  microTitle.href = micro.file.path;
  microTitle.setAttribute("data-href", micro.file.path);
  microTitle.style.textDecoration = "none";
  microTitle.style.fontSize = "0.8em";
  microTitle.textContent = `${microNumber}`;

  header.appendChild(dateRange);
  header.appendChild(microTitle);
  microElement.appendChild(header);
  
  // 创建旅程容器
  const journeysContainer = document.createElement("div");
  journeysContainer.style.display = "grid";
  journeysContainer.style.gridTemplateColumns = "repeat(auto-fill, minmax(140px, 1fr))";
  journeysContainer.style.gap = "6px";
  microElement.appendChild(journeysContainer);
  
  // 添加各种旅程
  Object.keys(JOURNEY_TYPES).forEach(type => {
    const journey = micro[type];
    if (!journey) return;
    
    const journeyElement = document.createElement("a");
    journeyElement.className = "internal-link";
    journeyElement.style.display = "flex";
    journeyElement.style.alignItems = "center";
    journeyElement.style.padding = "4px 8px";
    journeyElement.style.borderRadius = "4px";
    journeyElement.style.backgroundColor = "rgba(255, 255, 255, 0.05)";
    journeyElement.style.textDecoration = "none";
    journeyElement.style.color = "var(--text-normal)";
    journeyElement.style.fontSize = "0.8em";
    
    // 设置链接属性
    if (typeof journey === 'object' && journey.path) {
      // 这是 dataviewLink
      journeyElement.href = journey.path;
      journeyElement.setAttribute("data-href", journey.path);
      journeyElement.style.fontWeight = "500";
    } else if (typeof journey === 'string') {
      // 这是 String，需要创建新的笔记链接
      const journeyPath = `Nexus/旅程/${journey}`;
      journeyElement.href = journeyPath;
      journeyElement.setAttribute("data-href", journeyPath);
      journeyElement.style.opacity = "0.8";
      journeyElement.style.fontStyle = "italic";
    }
    
    // 添加图标和名称
    journeyElement.innerHTML = `${JOURNEY_TYPES[type]} ${journey.path ? journey.path.split('/').pop().replace('.md', '') : journey}`;
    
    journeysContainer.appendChild(journeyElement);
  });
  
  return microElement;
}

// 添加各类周期到容器中

ongoingMicroList.forEach(micro => {
  microListContainer.appendChild(createMicroElement(micro, "ongoing"));
});

plannedMicroList.forEach(micro => {
  microListContainer.appendChild(createMicroElement(micro, "planned"));
});

// 如果有未计划周期，添加未计划周期元素
if (unplannedCount > 0) {
  microListContainer.appendChild(createMicroElement(null, "unplanned"));
}

completedMicroList.forEach(micro => {
  microListContainer.appendChild(createMicroElement(micro, "completed"));
});


// 增加一些额外的方便点击链接的功能
document.addEventListener("click", function(event) {
  const target = event.target.closest(".journey-link");
  if (target) {
    event.preventDefault();
    const href = target.getAttribute("data-href");
    if (href) {
      app.workspace.openLinkText(href, "", false);
    }
  }
});

