/*
 * @作者: roam1n
 * @版本: 0.0.7
 * @最后更新: 2025-04-04
 */

// 从input {start: 开始时间, end: 结束时间, page: 当前页面}获取配置参数，如果没有提供则使用默认值
const START_TIME = input?.start ?? 7;  // 一天的开始时间（小时）
const END_TIME = input?.end ?? 26;     // 一天的结束时间（小时，可以超过24）
const CURRENT_PAGE_NAME = input?.page ?? dv.current().file.name;

const TODAY = new Date(CURRENT_PAGE_NAME);
TODAY.setHours(0, 0, 0, 0);

const CURRENT_MICRO = dv.pages(`"Nexus/周期"`).sort(p => p.file.name).find(p=>{
    if (!p.周期排期) return false;

    const start = new Date(p.开始);
    const count = p.周期排期.length * p.周期循环;
    return TODAY.getTime() >= start.getTime() && TODAY.getTime() <= (start.getTime() + count * 24 * 60 * 60 * 1000);
});

// config
const TIMELINE_HEIGHT = "80px";
const MIN_BLOCK_WIDTH = 30; // 最小时间块宽度（像素）
// "j": "#808080",  // 杂项任务 - 灰色

// 创建容器
const container = dv.container;
container.style.width = "100%";
container.style.height = TIMELINE_HEIGHT;
container.style.position = "relative";
container.style.backgroundColor = "var(--background-primary)";
container.style.borderRadius = "5px";
container.style.overflow = "hidden";
container.style.paddingLeft = "20px";   // 添加左内边距
container.style.paddingRight = "20px";  // 添加右内边距

// InfoContainer
const infoContainer = document.createElement("div");
infoContainer.style.display = "flex";
infoContainer.style.flexDirection = "row";
infoContainer.style.alignItems = "center";
infoContainer.style.justifyContent = "space-evenly";
infoContainer.style.width = "100%";
infoContainer.style.flexWrap = "nowrap";

container.appendChild(infoContainer);

// 添加周期和日期提示文字
const microInfo = document.createElement("a");
infoContainer.appendChild(microInfo);
microInfo.style.display = "block";
microInfo.style.position = "relative";
microInfo.style.width = "max-content";
microInfo.style.textAlign = "left";
microInfo.style.fontSize = "12px";
microInfo.style.fontWeight = "bold";
microInfo.style.color = "#808080";
microInfo.style.padding = "2px";
microInfo.style.borderRadius = "4px";
// cycleInfo.style.backgroundColor = "color-mix(in srgb, #808080 15%, transparent)";

// 添加旅程提示
const iconStyle = `width: 1em; height: 1em; display: inline-block; position: relative; top: 2px; margin-right: 4px;`;

const journeyInfo = document.createElement("div");
journeyInfo.style.display = "block";
journeyInfo.style.position = "relative";
journeyInfo.style.width = "max-content";
journeyInfo.style.flex = "1";
journeyInfo.style.fontSize = "12px";
journeyInfo.style.fontWeight = "bold";
journeyInfo.style.margin = "0 10px";

// 计算周期信息
if (CURRENT_MICRO) {

    const microStart = new Date(CURRENT_MICRO.开始);
    const dayDiff = Math.floor((TODAY.getTime() - microStart.getTime()) / (24 * 60 * 60 * 1000));
    const [macroCode, microCode] = CURRENT_MICRO.file.name.split('=');

    microInfo.classList.add('internal-link')
    microInfo.dataset['href'] = CURRENT_MICRO.file.path;
    microInfo.href = CURRENT_MICRO.file.path;
    microInfo.textContent = `${macroCode} 第${microCode}周期 ${dayDiff > 9 ? dayDiff + 1 : "0" + (dayDiff + 1)}${CURRENT_MICRO.周期排期[dayDiff % CURRENT_MICRO.周期排期.length].toUpperCase()}`;

    // 计算当前旅程提示内容
    function internalLink(path) {
        return `<a href=${path} data-href=${path} class='internal-link' style="color: inherit;">${path.split('/').last().replace('.md', '')}</a>`
    }

    const 学习 = CURRENT_MICRO.学习?.path ? internalLink(CURRENT_MICRO.学习.path) : '学习';
    const 实践 = CURRENT_MICRO.实践?.path ? internalLink(CURRENT_MICRO.实践.path) : '实践';
    const 练习 = CURRENT_MICRO.练习?.path ? internalLink(CURRENT_MICRO.练习.path) : '练习';

    const 娱乐 = internalLink("Nexus/面板/娱乐.md");
    const 运动 = internalLink("Nexus/面板/运动.md");
    const 拓展 = internalLink("Nexus/面板/拓展.md");
    const 更新 = internalLink("Nexus/面板/复习.md");

    const now = new Date();
    const curr_time = now.getHours() + now.getMinutes() / 60;
    
    // 获取当天的周期类型 (A, B, C)
    const microType = CURRENT_MICRO.周期排期[dayDiff % CURRENT_MICRO.周期排期.length].toUpperCase();

    // 定义时间段和对应的活动
    const timeRanges = [
        { start: 6.0, end: 9.0, activity: 练习, icon_type: 'r' },
        { start: 9.0, end: 9.5, activity: '早饭', icon_type: 'j' },
        { start: 9.5, end: 13.0, activity: microType === 'C' ? 更新 : 实践, icon_type: microType === 'C' ? 'r' : 't' },
        { start: 13.0, end: 14.0, activity: '午饭', icon_type: 'j' },
        { start: 14.0, end: 16.0, activity: microType === 'C' ? 运动 : 实践, icon_type: microType === 'C' ? 'N' : 't' },
        { start: 16.0, end: 17.0, activity: 运动, icon_type: 'N' },
        { start: 17.0, end: 18.0, activity: '晚饭/洗漱', icon_type: 'j' },
        { start: 18.0, end: 21.0, activity: microType === 'C' ? 娱乐 : 学习, icon_type: microType === 'C' ? 'B' : 's' },
        { start: 21.0, end: 22.0, activity: microType === 'C' ? 娱乐 : '复习/订阅', icon_type: microType === 'C' ? 'B' : 'r' },
        { start: 22.0, end: 23.0, activity: microType === 'C' ? 娱乐 : 拓展, icon_type: microType === 'C' ? 'B' : 'e' }
    ];
    
    // 根据当前时间确定推荐内容
    let infoContent = '睡觉'; // 默认值
    let infoIcon = 'a'; // 默认值   

    for (const range of timeRanges) {
        if (curr_time >= range.start && curr_time < range.end) {
            infoContent = typeof range.activity === 'object' 
                ? range.activity 
                : range.activity;
            infoIcon = range.icon_type;
            break;
        }
    }

    journeyInfo.style.color = `var(--color-task-${infoIcon})`;
    journeyInfo.innerHTML = `<span data-icon='${infoIcon}' style='${iconStyle}'></span>${infoContent}`;
} else {
    microInfo.textContent = `${TODAY.getFullYear()}年 无运行周期`;
    microInfo.style.color = "var(--text-muted)";
}

infoContainer.appendChild(journeyInfo);

// 快捷跳转
const jumpContainer = document.createElement("div");
jumpContainer.style.display = "flex";
jumpContainer.style.flexDirection = "row";
jumpContainer.style.alignItems = "center";
jumpContainer.style.justifyContent = "space-evenly";
jumpContainer.style.flexWrap = "nowrap";
jumpContainer.style.marginRight = "10px";

infoContainer.appendChild(jumpContainer);

const jumpButtons = [
    {icon_type: "N", href: "Nexus/面板/运动.md"},
]

jumpButtons.forEach(button => {
    const jumpButton = document.createElement("a");
    jumpButton.href = button.href;
    jumpButton.className = "internal-link";
    jumpButton.dataset['href'] = button.href;
    jumpButton.style.display = "flex";
    jumpButton.style.alignItems = "center";
    jumpButton.style.justifyContent = "center";
    jumpButton.style.borderRadius = "50%";
    jumpButton.style.cursor = "pointer";
    jumpButton.style.textDecoration = "none";
    jumpButton.style.transition = "all 0.3s ease";

    const icon = document.createElement("span");
    icon.dataset['icon'] = button.icon_type;
    icon.style = iconStyle;
    jumpButton.appendChild(icon);

    jumpContainer.appendChild(jumpButton);
});

// 添加自适应计算函数
function calculateScale() {
    const containerWidth = container.offsetWidth;
    const totalHours = END_TIME - START_TIME;
    const minWidthNeeded = totalHours * MIN_BLOCK_WIDTH;

    // 如果容器宽度小于最小所需宽度，调整刻度间隔
    const hourStep = Math.ceil(minWidthNeeded / containerWidth);
    return hourStep;
}

// 获取当前页面的任务
const tasks = dv.page(CURRENT_PAGE_NAME).file.tasks;

// 解析时间字符串，返回小时和分钟
function parseTimeString(timeStr) {
    const match = timeStr.match(/(\d{1,2}):(\d{2})/);
    if (!match) return null;
    return {
        hours: parseInt(match[1]),
        minutes: parseInt(match[2])
    };
}

// 将时间转换为相对位置（0.02-0.98）
function timeToPosition(hours, minutes) {
    const totalMinutes = hours * 60 + minutes;
    const startMinutes = START_TIME * 60;
    const endMinutes = END_TIME * 60;
    // 将0-1的范围映射到0.02-0.98
    const rawPosition = (totalMinutes - startMinutes) / (endMinutes - startMinutes);
    return 0.02 + (rawPosition * 0.96); // 0.96是0.98-0.02的差值
}

// 从任务文本中提取时间范围
function extractTimeRange(text) {
    const match = text.match(/(\d{1,2}:\d{2})-(\d{1,2}:\d{2})/);
    if (!match) return null;
    return {
        start: parseTimeString(match[1]),
        end: parseTimeString(match[2])
    };
}

// 时间刻度容器
const timeScaleContainer = document.createElement("div");
container.appendChild(timeScaleContainer);
timeScaleContainer.style.position = "relative";
timeScaleContainer.style.width = "100%";
timeScaleContainer.style.height = `calc(${TIMELINE_HEIGHT} - 20px)`;
timeScaleContainer.style.backgroundColor = "var(--background-primary)";
timeScaleContainer.style.borderRadius = "5px";
timeScaleContainer.style.marginTop = "0px";
timeScaleContainer.style.overflow = "hidden";

// 创建时间刻度
const hourStep = calculateScale();
for (let hour = START_TIME; hour <= END_TIME; hour += hourStep) {
    const position = timeToPosition(hour, 0);
    const tick = document.createElement("div");
    tick.style.position = "absolute";
    tick.style.left = `${position * 100}%`;
    tick.style.height = "20px";
    tick.style.borderLeft = "1px solid var(--text-normal)";
    tick.style.top = "24px";  // 将刻度线移到下方

    const label = document.createElement("div");
    label.style.position = "absolute";
    label.style.left = `${position * 100}%`;
    label.style.top = "4px";  // 将刻度标签移到更下方
    label.style.transform = "translateX(-50%)";
    label.style.fontSize = "12px";
    label.style.color = "var(--text-normal)";
    label.textContent = `${hour % 24}`;

    timeScaleContainer.appendChild(tick);
    timeScaleContainer.appendChild(label);
}

// 绘制任务时间块
tasks.forEach(task => {
    const timeRange = extractTimeRange(task.text);
    if (!timeRange) return;

    const startPos = timeToPosition(timeRange.start.hours, timeRange.start.minutes);
    const endPos = timeToPosition(timeRange.end.hours, timeRange.end.minutes);

    // 纹理间隔 每半小时一个纹理 对于整体时间线来说
    const textureWidth = 50.0 / (END_TIME - START_TIME);
    // 计算虚线的间隔 对于当前时间块来说
    const dashWidthPercent = textureWidth / (endPos - startPos);

    const block = document.createElement("div");
    block.style.position = "absolute";
    block.style.left = `${startPos * 100}%`;
    block.style.width = `${(endPos - startPos) * 100}%`;
    block.style.height = "16px";
    block.style.top = "28px";  // 将时间块移到上方
    block.style.backgroundColor = `var(--color-task-${task.status})`;
    
    // 使用线性渐变创建垂直虚线纹理
    const dashedLineColor = "rgba(255, 255, 255, 0.1)"; // 半透明白色
    block.style.backgroundImage = `linear-gradient(90deg, ${dashedLineColor} 1px, transparent 14px)`;
    block.style.backgroundSize = `${dashWidthPercent}% 100%`;
    block.style.backgroundPosition = "0 0";
    block.style.backgroundRepeat = "repeat-x";

    block.title = task.text;

    timeScaleContainer.appendChild(block);
});
