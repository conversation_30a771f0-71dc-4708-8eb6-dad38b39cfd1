---
<%*
/*
 * @作者: roam1n
 * @版本: 1.0.0
 * @最后更新: 2025-06-20
 * @功能: 文件内容归档和清理模板 - 将当前文件内容归档并清理正文
 */

// 检查是否为新建文件模式
if (tp.config.run_mode !== 0) {
    new Notice('该模板只能在创建时使用');
    return;
}

/**
 * 分离文件的 frontmatter 和正文内容
 * @param {string} fileContent - 完整文件内容
 * @returns {Object} 包含 frontmatter 和 body 的对象
 */
function separateContent(fileContent) {
    const frontmatterMatch = fileContent.match(/^---\n([\s\S]*?)\n---\n?([\s\S]*)$/);
    
    if (frontmatterMatch) {
        return {
            frontmatter: frontmatterMatch[1],
            body: frontmatterMatch[2] || ''
        };
    } else {
        // 没有 frontmatter 的情况
        return {
            frontmatter: '',
            body: fileContent
        };
    }
}

/**
 * 生成归档文件名
 * @returns {string} 格式化的文件名
 */
function generateArchiveFileName() {
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2);
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const hour = now.getHours().toString().padStart(2, '0');
    const minute = now.getMinutes().toString().padStart(2, '0');
    
    return `${year}${month}${day}-${hour}${minute}-update`;
}

/**
 * 创建归档文件内容
 * @param {string} originalFileName - 原文件名
 * @param {string} frontmatter - 原文件的 frontmatter
 * @param {string} body - 原文件的正文内容
 * @returns {string} 归档文件的完整内容
 */
function createArchiveContent(originalFileName, frontmatter, body) {
    let archiveContent = '---\n';
    archiveContent += '更新内容: "内容"\n';
    archiveContent += 'from:\n';
    archiveContent += `  - "[[${originalFileName}]]"\n`;
    archiveContent += '---\n\n';
    
    // 添加原文件的 frontmatter（用代码块包裹）
    if (frontmatter.trim()) {
        archiveContent += '```yaml\n';
        archiveContent += '---\n';
        archiveContent += frontmatter + '\n';
        archiveContent += '---\n';
        archiveContent += '```\n\n';
    }
    
    // 添加原文件的正文内容
    if (body.trim()) {
        archiveContent += body;
    }
    
    return archiveContent;
}

// 主执行逻辑
try {
    // 获取当前活动文件（要处理的目标文件）
    const targetFile = app.workspace.getActiveFile();
    if (!targetFile) {
        new Notice('未找到当前文件');
        return;
    }

    // 检查是否是模板文件本身
    if (targetFile.path.includes('Templater/')) {
        new Notice('不能对模板文件本身执行归档操作');
        return;
    }

    const originalFileName = targetFile.basename;

    // 读取目标文件内容
    const fileContent = await app.vault.read(targetFile);

    if (!fileContent.trim()) {
        new Notice('目标文件为空，无需归档');
        return;
    }

    // 分离 frontmatter 和正文内容
    const { frontmatter, body } = separateContent(fileContent);

    if (!body.trim()) {
        new Notice('目标文件正文为空，无需归档');
        return;
    }

    // 生成归档文件名和路径
    const archiveFileName = generateArchiveFileName();
    const archiveFilePath = `Archive/更新/${archiveFileName}`;

    // 创建归档文件内容
    const archiveContent = createArchiveContent(originalFileName, frontmatter, body);

    // 确保归档目录存在
    const archiveFolder = app.vault.getAbstractFileByPath('Archive/更新');
    if (!archiveFolder) {
        await app.vault.createFolder('Archive/更新');
    }

    // 清理目标文件正文，保留 frontmatter
    let newContent = '';
    if (frontmatter.trim()) {
        newContent = `---\n${frontmatter}\n---\n\n`;
    }

    // 更新目标文件
    await app.vault.modify(targetFile, newContent);

    // 将模板文件内容替换为归档内容，然后移动到归档位置
    await app.vault.modify(tp.config.active_file, archiveContent);
    await tp.file.move(archiveFilePath);

    new Notice(`文件内容已归档到: ${archiveFileName}.md`);
    new Notice(`原文件 "${originalFileName}" 正文已清理，frontmatter 已保留`);

} catch (error) {
    console.error('归档处理过程中发生错误:', error);
    new Notice('归档失败: ' + error.message);
}
-%>
更新内容: "内容"
from: 
  - "[[]]"
---
